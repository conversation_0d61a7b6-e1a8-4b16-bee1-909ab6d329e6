
import React, { useRef, useEffect, useState } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import ProjectCard from './ProjectCard'

gsap.registerPlugin(ScrollTrigger)

// <PERSON>-Yates shuffle algorithm to randomize array
const shuffleArray = <T,>(array: T[]): T[] => {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

// Define the project data structure
const projects = [
  {
    id: 1,

    projectUrl: 'https://www.pavementmaterials.co.za/',
    title: 'Robust E-commerce Platform for Building Materials Supplier',
    category: 'Web Development, SEO, Payment Gateway',
    technologies: 'Shopify, Handlebars, Bootstrap, Javascript, Liquid',
    imageUrl:
      'https://res.cloudinary.com/deejaydev/image/upload/f_auto,q_auto/v1/deejaydev/client-poster/lkv1nzxglxupdfpgezy1',
    description:
      'We built a strong e-commerce platform for Pavement Materials Group, a top supplier of construction materials in Southern Africa. Using Shopify, we made online sales of aggregates, asphalt, cement, and other products seamless. This improved their digital presence, simplified order management, and expanded their reach.'
  },
  {
    id: 2,

    projectUrl: 'https://cranebox.ng/',
    title: 'Empowering Brands with Sustainable Packaging Solutions',
    category: 'Web Development, Payment Gateway',
    technologies: 'Wordpress, Elementor, WooCommerce, Cloudinary, MySQL ',
    imageUrl:
      'https://res.cloudinary.com/deejaydev/image/upload/f_auto,q_auto/v1/deejaydev/client-poster/lwetknk1e20m4kyc9fmk',
    description:
      'Cranebox.ng leads in innovative, sustainable packaging, helping businesses of all sizes. Its high-quality, eco-friendly products enhance brand value, protect products through the supply chain, and reduce environmental impact.'
  },
  {
    id: 3,
    projectUrl: 'https://ubuntulifestyle.africa/',
    title: 'Culturally Rich E-commerce Platform for African Heritage Brand',
    category: 'Web Development, E-Commerce',
    technologies: 'WordPress, WooCommerce, Elementor, PHP, MySQL',
    imageUrl:
      'https://res.cloudinary.com/deejaydev/image/upload/f_auto,q_auto/v1/deejaydev/client-poster/bixssyi1qqu0y4yyjawc',
    description:
      'We created a vibrant, user-friendly e-commerce platform for Ubuntu Lifestyle, celebrating African heritage. Using WordPress and WooCommerce, they now showcase unique clothing and designs seamlessly. This boosted their online presence, customer engagement, and sales, helping share African culture globally.'
  },
  {
    id: 4,

    projectUrl: 'https://kingofgloryfoundation.co.za/',
    title:
      'Engaging Website for Non-Profit Driving Social Change Through Sport',
    category: 'Design, Web Development, Non-Profit',
    technologies: 'WordPress, Elementor, PHP, MySQL, anime.js',
    imageUrl:
      'https://res.cloudinary.com/deejaydev/image/upload/f_auto,q_auto/v1/deejaydev/client-poster/mpss7ixwjqr9fzfqvmkl',
    description:
      'We built a functional website for a non-profit in the Eastern Cape using WordPress and Elementor. The site highlights their mission to fight GBV and substance abuse, showcases programs, and promotes community empowerment, boosting visibility and supporter engagement.'
  },
  {
    id: 5,

    projectUrl: 'https://bulkmat.co.za/',
    title:
      'High-Volume  Platform for Builders Supplying Construction Materials',
    category: 'Web Development',
    technologies: 'BigCommerce, PHP, ZURB Foundation, Cloudflare',
    imageUrl:
      'https://res.cloudinary.com/deejaydev/image/upload/f_auto,q_auto/v1/deejaydev/client-poster/liwdwsboho3pgyhn7sf4',
    description:
      'We engineered a robust, high-performance e-commerce platform for Bulk Construction Materials, specializing in large-scale sales of building and civil construction products. Leveraging BigCommerce, we delivered a streamlined online store for their extensive catalog, including aggregates, bitumen, and concrete.'
  },
  {
    id: 6,

    projectUrl: 'https://compustore.co.za/',
    title: 'Complete Electronics & IT Product Online Solution',
    category: 'Web Development, E-commerce, Others',
    technologies: 'WooCommerce, GSAP, Slider Revolution, MailChimp',
    imageUrl:
      'https://res.cloudinary.com/deejaydev/image/upload/f_auto,q_auto/v1/deejaydev/client-poster/rfmwp4eyzumy1dyw8fbt',
    description:
      'We developed a strong e-commerce platform for a Durban based IT supplier using WooCommerce. The platform features an intuitive online store that simplifies buying a wide range of products, including laptops and networking equipment. Our solution improved sales efficiency, customer experience, and market expansion.'
  },
  {
    id: 7,

    projectUrl: 'https://rootsandroofing.com/',
    title: 'Secure Real Estate Investment Platform for Diaspora',
    category: 'Web Development, Real Estate, PropTech',
    technologies: 'Vercel, ReactJS, Supabase, Shadcn/Ui, Lovable',
    imageUrl:
      'https://res.cloudinary.com/deejaydev/image/upload/f_auto,q_auto/v1/deejaydev/client-poster/mlbbo4punijdfeppxpzr',
    description:
      'We developed a secure and transparent real estate investment platform for Roots & Roofing aimed at empowering Nigerians in the diaspora. Using Supabase for authentication and database management, the platform delivered a smooth user experience that tackles trust concerns and inefficient workflows.'
  },
  {
    id: 8,

    projectUrl: 'https://naijaservicepro.com/',
    title: 'Platform for Booking Verified Local Service Professionals',
    category: 'Web Development, Marketplace, Service Booking',
    technologies: 'Supabase, Git, Tailwind CSS, Vercel',
    imageUrl:
      'https://res.cloudinary.com/deejaydev/image/upload/f_auto,q_auto/v1/deejaydev/client-poster/ibfor7y16shujlqqwyc8',
    description:
      'Naija Service Pro is a secure and user-friendly online marketplace that connects individuals in Nigeria with verified local professionals. Built with ReactJS and Supabase, the platform offers an efficient way to find, compare, and book skilled service providers, promoting seamless project completion and support for local businesses.'
  },
  {
    id: 9,

    projectUrl:
      'https://www.figma.com/design/QWZQxiABupMGs07tB53ZVh/MoneySail--Transfer-Money-Online-?m=auto&t=4VaXxVjdjI17I5s3-1',
    title: 'Mobile App Design for an International Money Transfer Service',
    category: 'UI&UX',
    technologies: 'Figma',
    imageUrl:
      'https://res.cloudinary.com/deejaydev/image/upload/f_auto,q_auto/v1/deejaydev/client-poster/mj5zima2zdxhyhocozb0',
    description:
      'We designed a mobile app for a  money transfers. Designed with a complete screen flow of all features to take the app from idea to production, it let users to send/receive funds, view history, and manage accounts easily. This solution enhances financial convenience and user experience, streamlining digital payments.'
  },
  {
    id: 10,
    projectUrl:
      'https://www.figma.com/design/iq9baIq6awJ7F00H95qAlG/freud-UI-Kit--AI-Mental-Health-App--Community-?m=auto&t=4VaXxVjdjI17I5s3-1',
    title: 'AI-Driven Mental Health App UI Kit',
    category: 'UI&UX',
    technologies: 'Figma',
    imageUrl:
      'https://res.cloudinary.com/deejaydev/image/upload/f_auto,q_auto/v1/deejaydev/client-poster/mcdcczbxyubtlotfd74v',
    description:
      "We designed 'freud v1.2', a comprehensive AI-Driven Mental Health App UI Kit. Crafted in Figma, it features over 320 screens and 300 components, including dark mode support and a custom design system. This kit empowers clients to rapidly develop intuitive and engaging mental health applications, ensuring a high-quality user experience."
  },
  {
    id: 11,
    projectUrl:
      'https://www.figma.com/design/cYAQpEoOTtpn1LJFOvz8Or/Solana-Blockchain-Development-Website-Landing-Page--Community-?m=auto&t=4VaXxVjdjI17I5s3-1',
    title: 'Conceptual Web Design for a Blockchain Product',
    category: 'UI&UX',
    technologies: 'Figma',
    imageUrl:
      'https://res.cloudinary.com/deejaydev/image/upload/f_auto,q_auto/v1/deejaydev/client-poster/n2bg3o8afg4t6m7c9oos',
    description:
      'We developed a conceptual web design for a high-performance blockchain platform, focusing on a modern and engaging user experience. This demo showcases an intuitive interface for developers and users, highlighting key network metrics and community focus..'
  },
  {
    id: 12,
    projectUrl: '#',
    title: 'Website Design for Social Media Marketing Agency',
    category: 'UI/UX Design',
    technologies: 'Figma',
    imageUrl:
      'https://res.cloudinary.com/deejaydev/image/upload/f_auto,q_auto/v1/deejaydev/client-poster/geb4bdd5bsqnjhnldxyr',
    description:
      "We designed a modern and engaging website for a social media marketing agency, 'artistic.'. This UI/UX concept effectively showcases powerful social media strategies and brand transformation. The design features clear calls to action, expert insights, and testimonials, providing a professional online presence."
  }
]

const ProjectGrid = () => {
  const sectionRef = useRef<HTMLDivElement>(null)

  // Initialize randomized projects on component mount
  const [randomizedProjects] = useState(() => shuffleArray(projects))

  useEffect(() => {
    const projectElements =
      sectionRef.current?.querySelectorAll('.project-card')

    if (projectElements) {
      gsap.fromTo(
        projectElements,
        { opacity: 0, y: 30 },
        {
          opacity: 1,
          y: 0,
          duration: 0.6,
          stagger: 0.1,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: sectionRef.current,
            start: 'top 75%'
          }
        }
      )
    }

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [])

  return (
    <section ref={sectionRef} className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="text-left mb-12">
          <h2 className="text-2xl md:text-3xl font-bold mb-4 text-white">
            At KavaraDigital
          </h2>
          <p className="text-agency-white-muted max-w-3xl mb-8">
            We turn our passion for design into a creative approach to solve all
            technological challenges by producing beautiful, functional,
            user-centric solutions.
          </p>
          <p className="text-agency-white-muted">
            Here are some case studies of what we've built so far:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-16">
          {randomizedProjects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>
      </div>
    </section>
  )
}

export default ProjectGrid;
