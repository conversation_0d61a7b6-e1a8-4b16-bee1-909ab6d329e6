
import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

interface StoryItemProps {
  number: string;
  title: string;
  content: string;
  className?: string;
  animationDelay?: number;
}

const StoryItem = ({ number, title, content, className, animationDelay = 0 }: StoryItemProps) => {
  const itemRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);
    
    gsap.fromTo(
      itemRef.current,
      { 
        opacity: 0, 
        y: 30 
      },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.8,
        ease: "power2.out",
        scrollTrigger: {
          trigger: itemRef.current,
          start: "top 85%",
          end: "bottom 15%",
          toggleActions: "play none none none"
        },
        delay: animationDelay
      }
    );
    
    return () => {
      const triggers = ScrollTrigger.getAll().filter(
        trigger => trigger.vars.trigger === itemRef.current
      );
      triggers.forEach(trigger => trigger.kill());
    };
  }, [animationDelay]);

  return (
    <div ref={itemRef} className={`mb-16 ${className}`}>
      <div className="text-agency-green text-7xl md:text-8xl font-bold mb-2">{number}</div>
      <h3 className="text-2xl md:text-3xl font-bold mb-4">{title}</h3>
      <p className="text-agency-white-muted leading-relaxed">{content}</p>
    </div>
  );
};

const AboutStory = () => {
  const titleRef = useRef<HTMLHeadingElement>(null);

  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);
    
    gsap.fromTo(
      titleRef.current,
      { opacity: 0, y: 20 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.8,
        scrollTrigger: {
          trigger: titleRef.current,
          start: "top 85%",
          end: "bottom 15%",
          toggleActions: "play none none none"
        }
      }
    );
    
    return () => {
      const triggers = ScrollTrigger.getAll().filter(
        trigger => trigger.vars.trigger === titleRef.current
      );
      triggers.forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <h2
          ref={titleRef}
          className="text-3xl md:text-4xl font-bold mb-16 text-center sm:text-left"
        >
          Our Story
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-12 gap-y-6">
          <StoryItem
            number="01"
            title="Design"
            content="Once upon a time, in a world driven by technology, a group of talented designers came together with a shared vision. They believed that design could shape the way people interacted with digital products. With their passion for aesthetics and usability, they founded KavaraDigital Digital Product Agency's design department. Their mission was to create visually stunning and user-friendly interfaces that would leave a lasting impression."
            animationDelay={0.1}
          />

          <StoryItem
            number="02"
            title="Engineering"
            content="Meanwhile, a team of brilliant engineers was busy crafting the backbone of digital innovation. With their expertise in coding and development, they founded the engineering division of KavaraDigital. They believed that technology had the power to transform ideas into reality. Their mission was to build robust, scalable, and cutting-edge digital solutions that would push the boundaries of what was possible."
            animationDelay={0.2}
          />

          <StoryItem
            number="03"
            title="Project Management"
            content="In the midst of the creative and technical minds, a group of project managers emerged as the glue that held everything together. They understood the importance of effective communication, organization, and efficient execution. With their skills in planning and coordination, they founded KavaraDigital's project management team. Their mission was to ensure that every project ran smoothly, on time, and within budget."
            animationDelay={0.3}
          />

          <StoryItem
            number="04"
            title="Collaboration"
            content="At KavaraDigital, these three departments came together to form a cohesive and collaborative unit. They embraced the power of collaboration and recognized that their combined expertise would result in truly exceptional digital products. They believed that by working closely with their clients, understanding their needs, and involving them in the creative process, they could deliver solutions that surpassed expectations."
            animationDelay={0.4}
          />

          <StoryItem
            number="05"
            title="Client-Centric Approach"
            content="KavaraDigital understood that exceptional results come from understanding their clients' needs. The design, engineering, and project management teams at KavaraDigital made a conscious decision to put their unrivaled commitment to their clients. They placed their clients at the center of everything they did. They took the time to listen, understand their unique challenges, and tailor their services to meet their specific requirements. Their mission was to become trusted partners, guiding businesses on their digital journey."
            animationDelay={0.5}
          />

          <StoryItem
            number="06"
            title="Driving Success"
            content="With an unwavering commitment to quality and client satisfaction, KavaraDigital evolved to include a diverse range of projects and clients, from startups to large scale. From small to established enterprises, businesses sought out KavaraDigital for their expertise in creating digital products that delivered tangible results. KavaraDigital's success was driven by their passion for innovation, their dedication to quality, and their commitment to helping their clients succeed in the digital world."
            animationDelay={0.6}
          />
        </div>
      </div>
    </section>
  )
};

export default AboutStory;
