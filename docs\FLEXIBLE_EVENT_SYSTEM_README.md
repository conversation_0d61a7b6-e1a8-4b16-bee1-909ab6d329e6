# Flexible Event Landing Page System

## 🎯 Overview
A comprehensive, reusable event landing page system that can be used for various types of events including workshops, meetups, webinars, conferences, training sessions, sales events, and live streams.

## 🚀 Quick Start

### Current Event Pages
- **AI & Code Workshop**: `/code-and-ai-workshop` or `/training`
- **Tech Meetup**: `/tech-meetup` or `/meetup`
- **Legacy Event**: `/event` (backward compatibility)

### Creating a New Event

1. **Create Event Configuration**
   ```typescript
   // src/data/events/my-new-event.ts
   import { EventConfig } from '@/types/event'

   export const myNewEventConfig: EventConfig = {
     id: 'my-new-event',
     title: 'MY EVENT TITLE',
     subtitle: 'Event subtitle and description',
     type: 'workshop', // or 'meetup', 'webinar', etc.
     // ... other configuration
   }
   ```

2. **Create Event Page**
   ```typescript
   // src/pages/MyNewEvent.tsx
   import EventLandingTemplate from './EventLandingTemplate'
   import { myNewEventConfig } from '@/data/events/my-new-event'

   const MyNewEvent = () => {
     return <EventLandingTemplate config={myNewEventConfig} />
   }

   export default MyNewEvent
   ```

3. **Add Route**
   ```typescript
   // In src/App.tsx
   import MyNewEvent from './pages/MyNewEvent'
   
   // Add to routes:
   <Route path="/my-new-event" element={<MyNewEvent />} />
   
   // Add to hiddenRoutes for no bottom navigation:
   const hiddenRoutes = [..., '/my-new-event']
   ```

## 📋 Event Configuration Options

### Event Types
- `workshop` - Hands-on learning sessions
- `meetup` - Community networking events
- `webinar` - Online presentations
- `conference` - Large-scale events
- `training` - Educational sessions
- `sales` - Product/service sales events
- `livestream` - Live streaming events

### Required Fields
```typescript
{
  id: string,
  title: string,
  subtitle: string,
  description: string,
  type: EventType,
  details: EventDetails,
  pricing: EventPricing,
  benefits: EventBenefit[],
  testimonials: EventTestimonial[],
  urgencyMessage: string,
  contact: EventContact,
  registrationUrl: string,
  features: FeatureFlags,
  seo: SEOConfig
}
```

### Optional Fields
```typescript
{
  speakers?: EventSpeaker[],
  agenda?: EventAgenda[],
  spotsRemaining?: number,
  totalSpots?: number,
  theme?: ThemeConfig
}
```

### Feature Flags
Control which sections appear on your landing page:
```typescript
features: {
  countdown: boolean,      // Show countdown timer
  testimonials: boolean,   // Show testimonials section
  speakers: boolean,       // Show speakers section
  agenda: boolean,         // Show event agenda
  pricing: boolean,        // Show pricing section
  guarantee: boolean       // Show money-back guarantee
}
```

## 🎨 Customization

### Theme Customization
```typescript
theme: {
  primaryColor: '#936093',     // Main brand color
  accentColor: '#00ff00',      // Accent color
  backgroundImage: '/bg.jpg'   // Custom background
}
```

### Pricing Options
```typescript
pricing: {
  earlyBird: {
    price: 50000,              // Set to 0 for free events
    currency: '₦',
    deadline: 'August 15th'
  },
  regular: {
    price: 75000,
    currency: '₦'
  }
}
```

## 🔧 Component Structure

### Core Components
- `EventLandingTemplate` - Main template component
- `EventHeader` - Navigation header
- `EventHero` - Hero section with CTA
- `EventBenefits` - Benefits/features section
- `EventTestimonials` - Social proof section
- `EventSpeakers` - Speaker profiles (optional)
- `EventAgenda` - Event schedule (optional)
- `EventPricing` - Pricing options (optional)
- `EventCTA` - Final call-to-action

### Reusable Components
- `RegistrationModal` - Registration form modal
- `WhatsAppFloat` - Floating WhatsApp button

## 📱 Features

### Conversion Optimization
- ✅ AIDA framework implementation
- ✅ Multiple strategic CTAs
- ✅ Social proof and testimonials
- ✅ Urgency and scarcity elements
- ✅ Mobile-responsive design
- ✅ GSAP animations for engagement

### SEO Optimization
- ✅ Dynamic page titles
- ✅ Meta descriptions
- ✅ Open Graph tags
- ✅ Twitter Card tags
- ✅ Structured data ready

### User Experience
- ✅ Clean, distraction-free design
- ✅ No main navigation (conversion-focused)
- ✅ Fast loading and optimized
- ✅ Accessibility considerations

## 📊 Event Types Examples

### Workshop Configuration
```typescript
type: 'workshop',
features: {
  countdown: true,
  testimonials: true,
  speakers: false,
  agenda: true,
  pricing: true,
  guarantee: true
}
```

### Meetup Configuration
```typescript
type: 'meetup',
pricing: { earlyBird: { price: 0 }, regular: { price: 0 } }, // Free
features: {
  countdown: true,
  testimonials: true,
  speakers: true,
  agenda: true,
  pricing: false,
  guarantee: false
}
```

### Webinar Configuration
```typescript
type: 'webinar',
features: {
  countdown: true,
  testimonials: true,
  speakers: true,
  agenda: false,
  pricing: false,
  guarantee: false
}
```

## 🚀 Deployment

All event pages are automatically included in the build and can be accessed via their configured routes. The system maintains backward compatibility with the existing `/event` route.

## 📞 Support

For questions about the event system:
- Email: <EMAIL>
- Phone: +234 (0802) 902-0121

---

**Note**: This system is designed to be highly flexible and scalable. You can easily create new event types and customize existing ones without touching the core template code.
