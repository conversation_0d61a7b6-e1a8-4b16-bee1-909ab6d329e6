# Data Layer Documentation

This directory contains data and configuration files that are used across the application, following the separation of concerns principle.

## Files

### `pricing-packages.ts`

Contains all pricing package data for the digital agency, including:

- **Package interface**: TypeScript interface defining the structure of pricing packages
- **Packages array**: Complete list of all pricing plans with features, pricing, and payment URLs
- **SALE_END_DATE**: Configuration for sale countdown timer

#### Structure

```typescript
interface Package {
  name: string
  subtitle: string
  salePrice: string
  oldPrice?: string
  renewal?: string
  features: string[]
  monthly?: string
  cta: string
  highlight?: boolean
  new?: boolean
  oneTimePaymentUrl: string
  monthlyPaymentUrl?: string
}
```

#### Usage

```typescript
import { packages, Package, SALE_END_DATE } from '@/data/pricing-packages'
```

#### Benefits of this structure:

1. **Separation of Concerns**: Data is separated from UI components
2. **Maintainability**: Easy to update pricing, features, or payment URLs in one place
3. **Reusability**: Data can be imported and used in multiple components
4. **Type Safety**: Full TypeScript support with proper interfaces
5. **Scalability**: Easy to add new packages or modify existing ones

#### Making Changes

To update pricing packages:

1. Edit the `packages` array in `src/data/pricing-packages.ts`
2. Add/remove features, update pricing, or modify payment URLs
3. The changes will automatically reflect in all components using this data

#### Payment URLs

Each package has two payment URLs:
- `oneTimePaymentUrl`: For one-time payments
- `monthlyPaymentUrl`: For monthly subscription payments (optional)

The PricingSection component automatically routes users to the appropriate URL based on their subscription preference.
