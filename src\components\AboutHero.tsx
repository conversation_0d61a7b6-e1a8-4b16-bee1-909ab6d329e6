
import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import AnimatedFloatingBlocks from './AnimatedFloatingBlocks'

const AboutHero = () => {
  const titleRef = useRef<HTMLHeadingElement>(null)
  const descRef = useRef<HTMLParagraphElement>(null)

  useEffect(() => {
    const tl = gsap.timeline()

    tl.fromTo(
      titleRef.current,
      { opacity: 0, y: 30 },
      { opacity: 1, y: 0, duration: 0.8, ease: 'power3.out' }
    ).fromTo(
      descRef.current,
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.7, ease: 'power3.out' },
      '-=0.4'
    )

    return () => {
      tl.kill()
    }
  }, [])

  return (
    <div className="pt-24 pb-16 bg-gradient-overlay relative overflow-hidden">
      {/* Animated Floating Blocks */}
      <AnimatedFloatingBlocks />

      <div className="container mx-auto px-4 py-44 text-center relative z-10">
        <h1
          ref={titleRef}
          className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white"
        >
          About Us
        </h1>
        <p
          ref={descRef}
          className="text-agency-white-muted max-w-3xl mx-auto text-lg"
        >
          Welcome to KavaraDigital, where collaboration, expertise, and
          client-centricity intersect to shape the future of digital innovation.
        </p>
      </div>
    </div>
  )
}

export default AboutHero;
