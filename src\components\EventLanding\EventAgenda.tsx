import { Clock } from 'lucide-react'
import { EventConfig } from '@/types/event'

interface EventAgendaProps {
  config: EventConfig
}

const EventAgenda = ({ config }: EventAgendaProps) => {
  if (!config.agenda || config.agenda.length === 0) {
    return null
  }

  return (
    <section className="py-16 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Event Agenda
          </h2>
          <p className="text-agency-white-muted text-lg">
            Here's what we have planned for you
          </p>
        </div>

        <div className="space-y-6">
          {config.agenda.map((item, index) => (
            <div
              key={index}
              className="bg-agency-darker p-6 rounded-xl border border-agency-green/10 hover:border-agency-green/30 transition-all duration-300"
            >
              <div className="flex flex-col md:flex-row md:items-center gap-4">
                <div className="flex items-center gap-2 text-agency-green font-bold min-w-[120px]">
                  <Clock size={16} />
                  <span>{item.time}</span>
                </div>
                
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-white mb-2">{item.title}</h3>
                  <p className="text-agency-white-muted mb-2">{item.description}</p>
                  {item.speaker && (
                    <p className="text-agency-green text-sm font-medium">
                      Speaker: {item.speaker}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default EventAgenda
