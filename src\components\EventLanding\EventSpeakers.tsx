import { ExternalLink } from 'lucide-react'
import { EventConfig } from '@/types/event'

interface EventSpeakersProps {
  config: EventConfig
}

const EventSpeakers = ({ config }: EventSpeakersProps) => {
  if (!config.speakers || config.speakers.length === 0) {
    return null
  }

  return (
    <section className="py-16 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Meet Our Speakers
          </h2>
          <p className="text-agency-white-muted text-lg max-w-2xl mx-auto">
            Learn from industry experts and thought leaders
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {config.speakers.map((speaker, index) => (
            <div
              key={index}
              className="bg-agency-darker p-6 rounded-xl border border-agency-green/10 hover:border-agency-green/30 transition-all duration-300 text-center"
            >
              <div className="mb-4">
                <img 
                  src={speaker.image} 
                  alt={speaker.name}
                  className="w-24 h-24 rounded-full object-cover mx-auto mb-4"
                />
                <h3 className="text-xl font-bold text-white mb-1">{speaker.name}</h3>
                <p className="text-agency-green font-medium mb-3">{speaker.role}</p>
                <p className="text-agency-white-muted text-sm">{speaker.bio}</p>
              </div>

              {speaker.social && (
                <div className="flex justify-center gap-3 mt-4">
                  {speaker.social.linkedin && (
                    <a
                      href={speaker.social.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-agency-white-muted hover:text-agency-green transition-colors"
                    >
                      <ExternalLink size={16} />
                    </a>
                  )}
                  {speaker.social.twitter && (
                    <a
                      href={speaker.social.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-agency-white-muted hover:text-agency-green transition-colors"
                    >
                      <ExternalLink size={16} />
                    </a>
                  )}
                  {speaker.social.website && (
                    <a
                      href={speaker.social.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-agency-white-muted hover:text-agency-green transition-colors"
                    >
                      <ExternalLink size={16} />
                    </a>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default EventSpeakers
