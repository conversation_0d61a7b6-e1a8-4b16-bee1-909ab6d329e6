
import React, { useEffect, useRef } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const FeatureItem = ({
  number,
  title,
  description,
  index
}: {
  number: string
  title: string
  description: string
  index: number
}) => {
  const itemRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    gsap.fromTo(
      itemRef.current,
      { x: -30, opacity: 0 },
      {
        x: 0,
        opacity: 1,
        duration: 0.6,
        scrollTrigger: {
          trigger: itemRef.current,
          start: 'top 85%'
        },
        delay: index * 0.15
      }
    )
  }, [index])

  return (
    <div ref={itemRef} className="flex gap-4 mb-12">
      <div className="flex-shrink-0">
        <div className="w-10 h-10 rounded-full bg-agency-green flex items-center justify-center text-agency-dark font-bold">
          {number}
        </div>
      </div>
      <div>
        <h3 className="text-xl font-bold mb-3 text-white">{title}</h3>
        <p className="text-agency-white-muted">{description}</p>
      </div>
    </div>
  )
}

const FeaturesSection = () => {
  const sectionRef = useRef<HTMLDivElement>(null)
  const headingRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    gsap.fromTo(
      headingRef.current,
      { y: 30, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 0.7,
        scrollTrigger: {
          trigger: headingRef.current,
          start: 'top 85%'
        }
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach((t) => t.kill())
    }
  }, [])

  const features = [
    {
      number: '01',
      title: 'Expertise',
      description:
        'Our team consists of industry experts with years of experience in design and development.'
    },
    {
      number: '02',
      title: 'End-to-end approach',
      description:
        'We handle every aspect of product development from concept to launch and beyond.'
    },
    {
      number: '03',
      title: 'Results & collaborations',
      description:
        'We focus on delivering tangible results through close collaboration with your team.'
    },
    {
      number: '04',
      title: 'Cutting-edge technology',
      description:
        'We stay ahead of the curve, using the latest technologies and methodologies.'
    }
  ]

  return (
    <section
      ref={sectionRef}
      className="section-padding bg-agency-darker relative overflow-hidden"
    >
      <div className="container mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 lg:gap-16 items-center">
          <div ref={headingRef}>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-white">
              Why Choose KavaraDigital?
            </h2>
            <p className="text-agency-white-muted text-lg mb-8">
              We combine creative thinking with technical expertise to build
              digital products that make a difference.
            </p>
            <a
              href="#contact"
              className="inline-block px-8 py-3 bg-agency-green text-agency-dark font-medium rounded-md hover:bg-opacity-90 transition-all btn-glow"
            >
              Get Started
            </a>
          </div>

          <div>
            {features.map((feature, index) => (
              <FeatureItem
                key={feature.title}
                number={feature.number}
                title={feature.title}
                description={feature.description}
                index={index}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default FeaturesSection;
