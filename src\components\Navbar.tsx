
import React, { useState, useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { cn } from '@/lib/utils'
import { Link, useLocation } from 'react-router-dom'

const Navbar = () => {
  const [isCompact, setIsCompact] = useState(false)
  const location = useLocation()
  const containerRef = useRef<HTMLDivElement>(null)

  // Hide navbar on landing pages for better conversion focus
  const hiddenRoutes = ['/event']
  const shouldHide = hiddenRoutes.includes(location.pathname)

  if (shouldHide) return null

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY

      // Compact state - shrink navbar at ~800px scroll
      if (scrollY > 800) {
        if (!isCompact) {
          setIsCompact(true)
          // Animate to compact state
          if (containerRef.current) {
            gsap.to(containerRef.current, {
              scale: 0.85,
              duration: 0.3,
              ease: 'power2.out'
            })
          }
        }
      } else {
        if (isCompact) {
          setIsCompact(false)
          // Animate back to full size
          if (containerRef.current) {
            gsap.to(containerRef.current, {
              scale: 1,
              duration: 0.3,
              ease: 'power2.out'
            })
          }
        }
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [isCompact])

  const isActive = (path: string) => {
    if (path === '/' && location.pathname === '/') return true
    if (path !== '/' && location.pathname.startsWith(path)) return true
    return false
  }

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'What We Do', path: '/services' },
    { name: 'Our Pricing', path: '/pricing' },
    { name: 'How We Work', path: '/process' },
    { name: 'Case Studies', path: '/work' },
    { name: 'About Us', path: '/about' }
    // { name: 'Careers', path: '/#careers' }
  ]

  return (
    <header className="fixed top-4 left-4 right-4 z-50 transition-all duration-300">
      <div ref={containerRef} className="hidden md:block mx-auto max-w-6xl">
        <div
          className="rounded-2xl px-6 py-4 backdrop-blur-xl transition-all duration-300 border"
          style={{
            backgroundColor: `rgba(35, 20, 43, 0.95)`,
            borderColor: `rgba(147, 96, 147, 0.2)`,
            boxShadow: `0 25px 50px -12px rgba(6, 3, 9, 0.6)`
          }}
        >
          <div className="flex items-center justify-between">
            <Link to="/" className="flex items-center">
              <img
                src="/logo-01.svg"
                alt="KavaraDigital"
                className={cn(
                  'transition-all duration-300',
                  isCompact ? 'w-40 h-10' : 'w-50 h-12'
                )}
              />
            </Link>

            <nav className="flex items-center space-x-6">
              {navItems.map((item) => {
                const isNavItemActive = isActive(item.path)
                return (
                  <Link
                    key={item.name}
                    to={item.path}
                    className={cn(
                      'text-sm transition-all duration-200 px-3 py-2 rounded-lg',
                      isNavItemActive
                        ? 'text-agency-green font-medium bg-agency-green/10'
                        : 'text-agency-white-muted hover:text-agency-green hover:bg-agency-green/5'
                    )}
                  >
                    {item.name}
                  </Link>
                )
              })}
            </nav>

            <Link
              to="/contact"
              className={cn(
                'flex px-5 py-2 bg-agency-green text-agency-dark font-medium rounded-lg hover:bg-opacity-90 transition-all btn-glow',
                isCompact ? 'px-4 py-1.5 text-sm' : 'px-5 py-2'
              )}
            >
              Contact Us
            </Link>
          </div>
        </div>
      </div>

      {/* Mobile navbar - preserve original mobile behavior */}
      <div className="md:hidden fixed top-0 left-0 right-0 z-50 transition-all duration-300 w-full bg-agency-darker bg-opacity-95 shadow-md py-3">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <Link to="/" className="flex items-center">
              <img
                src="/logo-01.svg"
                alt="KavaraDigital"
                className="w-40 h-10"
              />
            </Link>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Navbar;
