import { useEffect, useRef, useState } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { EventConfig } from '@/types/event'
import EventHeader from '@/components/EventLanding/EventHeader'
import EventHero from '@/components/EventLanding/EventHero'
import EventBenefits from '@/components/EventLanding/EventBenefits'
import EventTestimonials from '@/components/EventLanding/EventTestimonials'
import EventSpeakers from '@/components/EventLanding/EventSpeakers'
import EventAgenda from '@/components/EventLanding/EventAgenda'
import EventPricing from '@/components/EventLanding/EventPricing'
import EventCTA from '@/components/EventLanding/EventCTA'
import WhatsAppFloat from '@/components/WhatsAppFloat'
import RegistrationModal from '@/components/RegistrationModal'
import { usePageTitle } from '@/hooks/usePageTitle'

gsap.registerPlugin(ScrollTrigger)

interface EventLandingTemplateProps {
  config: EventConfig
}

const EventLandingTemplate = ({ config }: EventLandingTemplateProps) => {
  const [timeLeft, setTimeLeft] = useState({
    days: 7,
    hours: 12,
    minutes: 45,
    seconds: 30
  })
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Set dynamic page title
  usePageTitle(config.seo.title)

  const openRegistrationModal = () => {
    setIsModalOpen(true)
  }

  const closeRegistrationModal = () => {
    setIsModalOpen(false)
  }

  // Countdown timer effect
  useEffect(() => {
    if (!config.features.countdown) return

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 }
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 }
        } else if (prev.hours > 0) {
          return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 }
        } else if (prev.days > 0) {
          return {
            ...prev,
            days: prev.days - 1,
            hours: 23,
            minutes: 59,
            seconds: 59
          }
        }
        return prev
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [config.features.countdown])

  // GSAP Animations
  useEffect(() => {
    // Clean up on component unmount
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [])

  return (
    <div className="bg-agency-dark min-h-screen text-white overflow-x-hidden">
      {/* SEO Meta Tags */}
      <head>
        <title>{config.seo.title}</title>
        <meta name="description" content={config.seo.description} />
        <meta name="keywords" content={config.seo.keywords.join(', ')} />
        {config.seo.ogImage && (
          <>
            <meta property="og:image" content={config.seo.ogImage} />
            <meta name="twitter:image" content={config.seo.ogImage} />
          </>
        )}
        <meta property="og:title" content={config.seo.title} />
        <meta property="og:description" content={config.seo.description} />
        <meta name="twitter:title" content={config.seo.title} />
        <meta name="twitter:description" content={config.seo.description} />
      </head>

      {/* Event Header */}
      <EventHeader config={config} />

      {/* Hero Section */}
      <EventHero 
        config={config} 
        timeLeft={timeLeft}
        onRegisterClick={openRegistrationModal}
      />

      {/* Benefits Section */}
      <EventBenefits config={config} />

      {/* Testimonials Section */}
      {config.features.testimonials && (
        <EventTestimonials config={config} />
      )}

      {/* Speakers Section */}
      {config.features.speakers && config.speakers && (
        <EventSpeakers config={config} />
      )}

      {/* Agenda Section */}
      {config.features.agenda && config.agenda && (
        <EventAgenda config={config} />
      )}

      {/* Pricing Section */}
      {config.features.pricing && (
        <EventPricing 
          config={config} 
          onRegisterClick={openRegistrationModal}
        />
      )}

      {/* Final CTA Section */}
      <EventCTA 
        config={config} 
        timeLeft={timeLeft}
        onRegisterClick={openRegistrationModal}
      />

      {/* WhatsApp Float Button */}
      <WhatsAppFloat
        phoneNumber={config.contact.whatsapp.number}
        message={config.contact.whatsapp.message}
      />

      {/* Registration Modal */}
      <RegistrationModal
        isOpen={isModalOpen}
        onClose={closeRegistrationModal}
        eventTitle={config.title}
        registrationUrl={config.registrationUrl}
      />
    </div>
  )
}

export default EventLandingTemplate
