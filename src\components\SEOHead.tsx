import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import { PAGE_TITLES } from '@/hooks/usePageTitle'

const SEOHead = () => {
  const location = useLocation()

  useEffect(() => {
    const pageConfig = PAGE_TITLES[location.pathname] || PAGE_TITLES['/']
    
    // Set document title
    document.title = pageConfig.title
    
    // Helper function to update or create meta tag
    const updateMetaTag = (selector: string, content: string, isProperty = false) => {
      let meta = document.querySelector(selector)
      if (meta) {
        meta.setAttribute('content', content)
      } else {
        meta = document.createElement('meta')
        if (isProperty) {
          meta.setAttribute('property', selector.replace('meta[property="', '').replace('"]', ''))
        } else {
          meta.setAttribute('name', selector.replace('meta[name="', '').replace('"]', ''))
        }
        meta.setAttribute('content', content)
        document.head.appendChild(meta)
      }
    }
    
    // Update basic meta tags
    if (pageConfig.description) {
      updateMetaTag('meta[name="description"]', pageConfig.description)
    }
    
    if (pageConfig.keywords) {
      updateMetaTag('meta[name="keywords"]', pageConfig.keywords)
    }
    
    // Update Open Graph meta tags
    updateMetaTag('meta[property="og:title"]', pageConfig.title, true)
    if (pageConfig.description) {
      updateMetaTag('meta[property="og:description"]', pageConfig.description, true)
    }
    updateMetaTag('meta[property="og:type"]', 'website', true)
    updateMetaTag('meta[property="og:url"]', window.location.href, true)
    updateMetaTag('meta[property="og:site_name"]', 'Kavara Digital', true)
    updateMetaTag('meta[property="og:image"]', `${window.location.origin}/logo-01.svg`, true)
    
    // Update Twitter Card meta tags
    updateMetaTag('meta[name="twitter:card"]', 'summary_large_image')
    updateMetaTag('meta[name="twitter:site"]', '@kavaradigital')
    updateMetaTag('meta[name="twitter:creator"]', '@kavaradigital')
    updateMetaTag('meta[name="twitter:title"]', pageConfig.title)
    if (pageConfig.description) {
      updateMetaTag('meta[name="twitter:description"]', pageConfig.description)
    }
    updateMetaTag('meta[name="twitter:image"]', `${window.location.origin}/logo-01.svg`)
    
    // Update canonical URL
    let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement
    if (canonical) {
      canonical.href = window.location.href
    } else {
      canonical = document.createElement('link')
      canonical.rel = 'canonical'
      canonical.href = window.location.href
      document.head.appendChild(canonical)
    }
    
    // Update robots meta tag
    updateMetaTag('meta[name="robots"]', 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1')
    
    // Update viewport meta tag
    updateMetaTag('meta[name="viewport"]', 'width=device-width, initial-scale=1.0')
    
    // Update charset
    let charset = document.querySelector('meta[charset]')
    if (!charset) {
      charset = document.createElement('meta')
      charset.setAttribute('charset', 'UTF-8')
      document.head.insertBefore(charset, document.head.firstChild)
    }
    
    // Update language
    document.documentElement.lang = 'en'
    
  }, [location.pathname])

  return null // This component doesn't render anything
}

export default SEOHead
