import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'

const StructuredData = () => {
  const location = useLocation()

  useEffect(() => {
    // Remove existing structured data
    const existingScript = document.querySelector('script[type="application/ld+json"]')
    if (existingScript) {
      existingScript.remove()
    }

    let structuredData: any = {}

    // Base organization data
    const organizationData = {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'Kavara Digital',
      url: 'https://kavaradigital.online',
      logo: 'https://kavaradigital.online/logo-01.svg',
      description:
        'Premium web development and digital marketing agency specializing in custom websites, SEO, and AI solutions for business growth.',
      address: {
        '@type': 'PostalAddress',
        streetAddress: 'No 22, Off Keji Olajide Street',
        addressLocality: 'Lekki',
        addressRegion: 'Lagos',
        postalCode: '023401',
        addressCountry: 'NG'
      },
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '+234-0704-603-8430',
        contactType: 'customer service',
        email: '<EMAIL>',
        availableLanguage: 'English'
      },
      sameAs: [
        'https://twitter.com/kavaradigital',
        'https://linkedin.com/company/kavaradigital'
      ],
      foundingDate: '2020',
      numberOfEmployees: '10-50',
      areaServed: {
        '@type': 'Country',
        name: 'Nigeria'
      },
      serviceArea: {
        '@type': 'GeoCircle',
        geoMidpoint: {
          '@type': 'GeoCoordinates',
          latitude: '6.5244',
          longitude: '3.3792'
        },
        geoRadius: '100000'
      }
    }

    // Page-specific structured data
    switch (location.pathname) {
      case '/':
        structuredData = {
          '@context': 'https://schema.org',
          '@graph': [
            organizationData,
            {
              '@type': 'WebSite',
              name: 'Kavara Digital',
              url: 'https://kavaradigital.online',
              description:
                'Premium web development and digital marketing agency',
              publisher: {
                '@id': 'https://kavaradigital.online#organization'
              },
              potentialAction: {
                '@type': 'SearchAction',
                target:
                  'https://kavaradigital.online/search?q={search_term_string}',
                'query-input': 'required name=search_term_string'
              }
            }
          ]
        }
        break

      case '/services':
        structuredData = {
          "@context": "https://schema.org",
          "@graph": [
            organizationData,
            {
              "@type": "Service",
              "name": "Web Development Services",
              "provider": organizationData,
              "description": "Custom web development, SEO optimization, digital marketing, and AI integration services",
              "serviceType": "Web Development",
              "areaServed": "Nigeria",
              "hasOfferCatalog": {
                "@type": "OfferCatalog",
                "name": "Web Development Services",
                "itemListElement": [
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "Service",
                      "name": "Custom Web Development"
                    }
                  },
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "Service",
                      "name": "SEO Optimization"
                    }
                  },
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "Service",
                      "name": "Digital Marketing"
                    }
                  }
                ]
              }
            }
          ]
        }
        break

      case '/contact':
        structuredData = {
          "@context": "https://schema.org",
          "@graph": [
            organizationData,
            {
              "@type": "ContactPage",
              "name": "Contact Kavara Digital",
              "description": "Get in touch with Kavara Digital for web development and digital marketing services",
              "mainEntity": organizationData
            }
          ]
        }
        break

      case '/event':
        structuredData = {
          "@context": "https://schema.org",
          "@type": "Event",
          "name": "AI & Code Workshop",
          "description": "Master your code with our comprehensive, all-hands-on physical workshop. Integrate powerful AI tools for smarter, faster development.",
          "startDate": "2024-08-23T09:00:00+01:00",
          "endDate": "2024-08-23T16:00:00+01:00",
          "eventStatus": "https://schema.org/EventScheduled",
          "eventAttendanceMode": "https://schema.org/OfflineEventAttendanceMode",
          "location": {
            "@type": "Place",
            "name": "Kavara Digital Workshop Center",
            "address": {
              "@type": "PostalAddress",
              "addressLocality": "Lagos",
              "addressCountry": "NG"
            }
          },
          "organizer": organizationData,
          "offers": {
            "@type": "Offer",
            "name": "Workshop Registration",
            "price": "50000",
            "priceCurrency": "NGN",
            "availability": "https://schema.org/InStock",
            "url": "https://forms.gle/T7gPtiTs1RJiK5E76"
          },
          "performer": organizationData,
          "audience": {
            "@type": "Audience",
            "audienceType": "Developers, Programmers, Tech Enthusiasts"
          }
        }
        break

      default:
        structuredData = organizationData
    }

    // Add structured data to head
    const script = document.createElement('script')
    script.type = 'application/ld+json'
    script.textContent = JSON.stringify(structuredData)
    document.head.appendChild(script)

  }, [location.pathname])

  return null
}

export default StructuredData
