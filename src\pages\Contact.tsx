
import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Mail, Phone, MapPin } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import ContactForm from '@/components/ContactForm';
import FaqSection from '@/components/FaqSection';
import AnimatedFloatingBlocks from '@/components/AnimatedFloatingBlocks'
import { Button } from '@/components/ui/button'

gsap.registerPlugin(ScrollTrigger)

const Contact = () => {
  const heroRef = useRef<HTMLDivElement>(null)
  const infoRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const heroTl = gsap.timeline()
    heroTl.fromTo(
      heroRef.current?.querySelector('h1'),
      { opacity: 0, y: 30 },
      { opacity: 1, y: 0, duration: 0.8 }
    )
    heroTl.fromTo(
      heroRef.current?.querySelector('p'),
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.8 },
      '-=0.6'
    )

    gsap.fromTo(
      infoRef.current?.querySelectorAll('.contact-info-item'),
      { opacity: 0, y: 20 },
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        stagger: 0.2,
        scrollTrigger: {
          trigger: infoRef.current,
          start: 'top 80%'
        }
      }
    )

    // Clean up on component unmount
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [])

  return (
    <div className="bg-agency-dark min-h-screen overflow-x-hidden pb-24 md:pb-0">
      <Navbar />

      {/* Hero Section */}
      <div
        ref={heroRef}
        className="pt-24 pb-16 bg-gradient-overlay relative overflow-hidden"
      >
        {/* Animated Floating Blocks */}
        <AnimatedFloatingBlocks />

        <div className="container mx-auto px-4 py-44 text-center relative z-10">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white">
            Contact Us
          </h1>
          <p className="text-agency-white-muted max-w-3xl mx-auto">
            Get in touch with us today and let us help you with any questions or
            inquiries you may have.
          </p>
        </div>
      </div>

      {/* Contact Info Section */}
      <div ref={infoRef} className="py-12 bg-agency-dark text-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-center gap-8 mb-12">
            <a
              href="mailto:<EMAIL>"
              className="contact-info-item flex items-center justify-center gap-3 bg-agency-darker p-5 rounded-md border border-white/5 hover:border-agency-green/30 transition-all"
            >
              <Mail className="text-agency-green" size={20} />
              <span><EMAIL></span>
            </a>
            <a
              href="tel:+23407046038430"
              className="contact-info-item flex items-center justify-center gap-3 bg-agency-darker p-5 rounded-md border border-white/5 hover:border-agency-green/30 transition-all"
            >
              <Phone className="text-agency-green" size={20} />
              <span>+234 (0704) 603-8430</span>
            </a>
            <a
              href="#"
              className="contact-info-item flex items-center justify-center gap-3 bg-agency-darker p-5 rounded-md border border-white/5 hover:border-agency-green/30 transition-all"
            >
              <MapPin className="text-agency-green" size={20} />
              <span>Get Location</span>
            </a>
          </div>

          {/* Contact Form Section */}
          <ContactForm />
        </div>
      </div>

      {/* Operating Days */}
      <div className="py-8 bg-agency-darker text-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0 flex items-center gap-8">
              <div>
                <p className="text-agency-white-muted">Operating Days</p>
                <p className="font-medium">Monday - Friday 9:00am - 5:00pm</p>
                <p className="font-medium">Saturday - Sunday Closed</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <span className="text-agency-white-muted mr-2">
                Stay Connected
              </span>

              <a
                href="x.com/KavaraDigital"
                target="_blank"
                className="text-white hover:text-agency-green p-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  height="24"
                  width="24"
                  viewBox="0.254 0.25 500 451.95400000000006"
                  className="w-5 h-5"
                >
                  <div
                    id="in-page-channel-node-id"
                    data-channel-name="in_page_channel_LUNtre"
                  />
                  <path
                    d="M394.033.25h76.67L303.202 191.693l197.052 260.511h-154.29L225.118 294.205 86.844 452.204H10.127l179.16-204.77L.254.25H158.46l109.234 144.417zm-26.908 406.063h42.483L135.377 43.73h-45.59z"
                    fill="#FFF"
                  />
                </svg>
              </a>
              <a
                href="https://instagram.com/kavaradigital"
                target="_blank"
                className="text-white hover:text-agency-green p-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-5 h-5"
                >
                  <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                  <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                </svg>
              </a>
              <a
                href="https://fb.com"
                target="_blank"
                className="text-white hover:text-agency-green p-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-5 h-5"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <FaqSection />

      <Footer />
    </div>
  )
}

export default Contact;
