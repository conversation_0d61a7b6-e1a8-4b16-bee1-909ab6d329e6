
import React, { useEffect, useRef } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Paintbrush, Code, ClipboardList } from 'lucide-react';
import { Link } from 'react-router-dom'

gsap.registerPlugin(ScrollTrigger)

const ServiceCard = ({
  icon: Icon,
  title,
  urlId,
  description,
  index
}: {
  icon: React.ElementType
  title: string
  description: string
  index: number
  urlId: string
}) => {
  const cardRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    gsap.fromTo(
      cardRef.current,
      {
        y: 50,
        opacity: 0
      },
      {
        y: 0,
        opacity: 1,
        duration: 0.5,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: cardRef.current,
          start: 'top 85%'
        },
        delay: index * 0.2
      }
    )
  }, [index])

  return (
    <div
      ref={cardRef}
      className="bg-agency-darker p-6 md:p-8 rounded-lg border border-white/10 flex flex-col items-center md:items-start text-center md:text-left card-hover"
    >
      <div className="w-12 h-12 bg-agency-green bg-opacity-10 rounded-full flex items-center justify-center mb-6">
        <Icon className="w-6 h-6 text-agency-green" />
      </div>
      <h3 className="text-xl font-bold mb-4 text-white">{title}</h3>
      <p className="text-agency-white-muted mb-6">{description}</p>
      <Link
        to={`/services#${urlId}`}
        className="text-agency-green hover:underline inline-flex items-center mt-auto"
      >
        Learn more
      </Link>
    </div>
  )
}

const ServicesSection = () => {
  const sectionRef = useRef<HTMLDivElement>(null)
  const headingRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    gsap.fromTo(
      headingRef.current,
      { y: 30, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 0.6,
        scrollTrigger: {
          trigger: headingRef.current,
          start: 'top 85%'
        }
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach((t) => t.kill())
    }
  }, [])

  const services = [
    {
      urlId: 'design',
      icon: Paintbrush,
      title: 'Design',
      description:
        'We create intuitive designs that help users achieve their goals efficiently and enjoyably.'
    },
    {
      urlId: 'engineering',
      icon: Code,
      title: 'Engineering',
      description:
        'We build scalable, high-performance applications that are reliable and maintainable.'
    },
    {
      urlId: 'project-management',
      icon: ClipboardList,
      title: 'Project Management',
      description:
        'We ensure your project stays on track, on budget, and delivers maximum business value.'
    }
  ]

  return (
    <section
      id="services"
      ref={sectionRef}
      className="section-padding bg-agency-dark relative overflow-hidden"
    >
      <div className="container mx-auto">
        <div ref={headingRef} className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-white">
            Our Services
          </h2>
          <p className="text-agency-white-muted max-w-2xl mx-auto">
            We provide end-to-end solutions for digital products that make an
            impact
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
          {services.map((service, index) => (
            <ServiceCard
              urlId={service.urlId}
              key={service.title}
              icon={service.icon}
              title={service.title}
              description={service.description}
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  )
}

export default ServicesSection;
