import React, { useEffect, useRef } from 'react'
import gsap from 'gsap'

const AnimatedFloatingBlocks = () => {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!containerRef.current) return

    const blocks = containerRef.current.children
    const animations: gsap.core.Tween[] = []

    Array.from(blocks).forEach((block, index) => {
      // Random initial positions within viewport
      const randomX = Math.random() * (window.innerWidth - 100)
      const randomY = Math.random() * (window.innerHeight - 100)

      // Set initial state with GSAP
      gsap.set(block, {
        x: randomX,
        y: randomY,
        rotation: Math.random() * 360,
        opacity: 0.5 + Math.random() * 0.8, // Random opacity between 0.1 and 0.4
        scale: 0.5 + Math.random() * 1.5 // Random scale between 0.5 and 1
      })

      // Create floating animation timeline
      const tl = gsap.timeline({ repeat: -1 })

      // Floating movement
      const moveAnimation = gsap.to(block, {
        x: `+=${(Math.random() - 0.5) * 400}`,
        y: `+=${(Math.random() - 0.5) * 400}`,
        rotation: `+=${(Math.random() - 0.5) * 360}`,
        duration: 15 + Math.random() * 10, // 15-25 seconds
        ease: 'none',
        repeat: -1,
        yoyo: true,
        delay: index * 0.5
      })

      // Scale pulsing animation
      const scaleAnimation = gsap.to(block, {
        scale: `+=${(Math.random() - 0.5) * 0.4}`,
        duration: 4 + Math.random() * 3, // 4-7 seconds
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true,
        delay: index * 0.3
      })

      // Opacity breathing effect
      const opacityAnimation = gsap.to(block, {
        opacity: `+=${(Math.random() - 0.5) * 0.2}`,
        duration: 6 + Math.random() * 4, // 6-10 seconds
        ease: 'power1.inOut',
        repeat: -1,
        yoyo: true,
        delay: index * 0.2
      })

      // Rotation animation
      const rotationAnimation = gsap.to(block, {
        rotation: `+=${360 + Math.random() * 180}`,
        duration: 20 + Math.random() * 10, // 20-30 seconds
        ease: 'none',
        repeat: -1,
        delay: index * 0.4
      })

      animations.push(
        moveAnimation,
        scaleAnimation,
        opacityAnimation,
        rotationAnimation
      )
    })

    // Cleanup function
    return () => {
      animations.forEach((animation) => animation.kill())
    }
  }, [])

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-none overflow-hidden z-0"
    >
      {/* Geometric Block 1 - Square */}
      <div className="absolute w-8 h-8 bg-agency-green/10 border border-agency-green/20 backdrop-blur-sm"></div>

      {/* Geometric Block 2 - Diamond */}
      <div className="absolute w-6 h-6 bg-agency-green/15 border border-agency-green/30 backdrop-blur-sm transform rotate-45"></div>

      {/* Geometric Block 3 - Rectangle */}
      <div className="absolute w-12 h-4 bg-agency-green/8 border border-agency-green/15 backdrop-blur-sm"></div>

      {/* Geometric Block 4 - Circle */}
      <div className="absolute w-10 h-10 bg-agency-green/12 border border-agency-green/25 rounded-full backdrop-blur-sm"></div>

      {/* Geometric Block 5 - Triangle */}
      <div
        className="absolute w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-agency-green/20"
        style={{ filter: 'drop-shadow(0 0 8px rgba(57, 255, 20, 0.2))' }}
      ></div>

      {/* Geometric Block 6 - Hexagon */}
      <div
        className="absolute w-8 h-8 bg-agency-green/10 border border-agency-green/20 backdrop-blur-sm"
        style={{
          clipPath:
            'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
          filter: 'drop-shadow(0 0 8px rgba(57, 255, 20, 0.2))'
        }}
      ></div>

      {/* Geometric Block 7 - Small Square */}
      <div className="absolute w-4 h-4 bg-agency-green/20 border border-agency-green/40 backdrop-blur-sm"></div>

      {/* Geometric Block 8 - Oval */}
      <div className="absolute w-16 h-8 bg-agency-green/8 border border-agency-green/15 rounded-full backdrop-blur-sm"></div>

      {/* Geometric Block 9 - Pentagon */}
      <div
        className="absolute w-6 h-6 bg-agency-green/15 border border-agency-green/30 backdrop-blur-sm"
        style={{
          clipPath: 'polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)',
          filter: 'drop-shadow(0 0 8px rgba(57, 255, 20, 0.2))'
        }}
      ></div>

      {/* Geometric Block 10 - Line */}
      <div
        className="absolute w-20 h-0.5 bg-gradient-to-r from-transparent via-agency-green/30 to-transparent"
        style={{ filter: 'drop-shadow(0 0 8px rgba(57, 255, 20, 0.2))' }}
      ></div>

      {/* Geometric Block 11 - Small Circle */}
      <div className="absolute w-3 h-3 bg-agency-green/25 rounded-full backdrop-blur-sm"></div>

      {/* Geometric Block 12 - Thin Rectangle */}
      <div className="absolute w-8 h-1 bg-agency-green/20 backdrop-blur-sm"></div>
    </div>
  )
}

export default AnimatedFloatingBlocks
