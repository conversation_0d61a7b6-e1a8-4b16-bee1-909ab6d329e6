import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

const ScrollToTop = () => {
  const { pathname } = useLocation()

  // Disable browser's scroll restoration
  useEffect(() => {
    if ('scrollRestoration' in history) {
      history.scrollRestoration = 'manual'
    }
  }, [])

  useEffect(() => {
    // Multiple methods to ensure scroll to top works across all browsers

    // Method 1: Modern scrollTo with instant behavior
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'instant'
    })

    // Method 2: Fallback for older browsers
    document.documentElement.scrollTop = 0
    document.body.scrollTop = 0

    // Method 3: Additional fallback using window properties
    if (window.pageYOffset !== 0) {
      window.scrollTo(0, 0)
    }

    // Small delay to ensure DOM is ready, then refresh ScrollTrigger
    const timer = setTimeout(() => {
      ScrollTrigger.refresh()
    }, 100)

    return () => clearTimeout(timer)
  }, [pathname])

  return null
}

export default ScrollToTop
