export interface EventDetails {
  date: string
  time: string
  location: string
  duration?: string
}

export interface EventPricing {
  earlyBird: {
    price: number
    currency: string
    deadline?: string
  }
  regular: {
    price: number
    currency: string
  }
  discount?: {
    percentage: number
    code?: string
  }
}

export interface EventBenefit {
  icon: string
  title: string
  description: string
}

export interface EventTestimonial {
  name: string
  role: string
  content: string
  rating: number
  image?: string
}

export interface EventSpeaker {
  name: string
  role: string
  bio: string
  image: string
  social?: {
    linkedin?: string
    twitter?: string
    website?: string
  }
}

export interface EventAgenda {
  time: string
  title: string
  description: string
  speaker?: string
}

export interface EventContact {
  phone: string
  email: string
  whatsapp: {
    number: string
    message: string
  }
}

export interface EventConfig {
  // Basic Info
  id: string
  title: string
  subtitle: string
  description: string
  type: 'workshop' | 'meetup' | 'webinar' | 'conference' | 'training' | 'sales' | 'livestream'
  
  // Event Details
  details: EventDetails
  
  // Pricing
  pricing: EventPricing
  
  // Content
  benefits: EventBenefit[]
  testimonials: EventTestimonial[]
  speakers?: EventSpeaker[]
  agenda?: EventAgenda[]
  
  // Marketing
  urgencyMessage: string
  spotsRemaining?: number
  totalSpots?: number
  
  // Contact & Registration
  contact: EventContact
  registrationUrl: string
  
  // Customization
  theme?: {
    primaryColor?: string
    accentColor?: string
    backgroundImage?: string
  }
  
  // Features
  features: {
    countdown: boolean
    testimonials: boolean
    speakers: boolean
    agenda: boolean
    pricing: boolean
    guarantee: boolean
  }
  
  // SEO
  seo: {
    title: string
    description: string
    keywords: string[]
    ogImage?: string
  }
}

export type EventType = EventConfig['type']
