
import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

interface ProcessStepProps {
  number: string;
  title: string;
  description: string;
  index: number;
}

const ProcessStep = ({ number, title, description, index }: ProcessStepProps) => {
  const stepRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    gsap.fromTo(
      stepRef.current,
      { opacity: 0, y: 30 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.6,
        scrollTrigger: {
          trigger: stepRef.current,
          start: "top 85%",
        },
        delay: 0.1 * index
      }
    );
  }, [index]);

  return (
    <div ref={stepRef} className="relative">
      <div className="text-6xl md:text-7xl lg:text-8xl font-bold text-agency-green opacity-80 mb-3">
        {number}
      </div>
      <h3 className="text-xl md:text-2xl font-bold mb-4 text-white">{title}</h3>
      <p className="text-agency-white-muted">{description}</p>
    </div>
  )
};

export default ProcessStep;
