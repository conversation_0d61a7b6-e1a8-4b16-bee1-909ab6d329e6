
import { Link } from 'react-router-dom';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';

const ThankYou = () => {
  return (
    <div className="bg-agency-dark min-h-screen overflow-x-hidden flex flex-col">
      <SEOHead
        title="Thank You for Your Purchase"
        description="We appreciate your business."
        domain="www.kavaradigital.online"
      />
      <Navbar />
      <main className="flex-grow flex flex-col items-center justify-center text-center px-4">
        <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">Thank You!</h1>
        <p className="text-lg md:text-xl text-gray-300 mb-8">
          Your order has been received and is now being processed.
        </p>
        <Link
          to="/"
          className="bg-agency-primary text-white font-bold py-3 px-6 rounded-lg hover:bg-agency-primary-dark transition-colors duration-300"
        >
          Return to Homepage
        </Link>
      </main>
      <Footer />
    </div>
  );
};

export default ThankYou;
