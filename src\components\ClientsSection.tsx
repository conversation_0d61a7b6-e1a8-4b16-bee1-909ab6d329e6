
import React, { useEffect, useRef } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const ClientsSection = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const headingRef = useRef<HTMLHeadingElement>(null);
  const logoContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: sectionRef.current,
        start: "top 80%",
      }
    });

    tl.fromTo(
      headingRef.current,
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6 }
    ).fromTo(
      logoContainerRef.current?.children,
      { y: 30, opacity: 0 },
      { y: 0, opacity: 1, stagger: 0.1, duration: 0.5 },
      "-=0.3"
    );

    return () => {
      if (tl.scrollTrigger) {
        tl.scrollTrigger.kill();
      }
      tl.kill();
    };
  }, []);

  const clients = [
    {
      name: 'https://bulkmat.co.za/',
      logo: '/client-logos/client-logo-(9).png'
    },
    {
      name: 'https://purdon.co.za/',
      logo: '/client-logos/client-logo-(5).png'
    },
    {
      name: 'https://geosynthetics.co.za/',
      logo: '/client-logos/client-logo-(2).png'
    },
    {
      name: 'https://uct.ac.za/',
      logo: '/client-logos/client-logo-(4).png'
    },
    {
      name: 'https://www.pavementmaterials.co.za/',
      logo: '/client-logos/client-logo-(3).png'
    },
    {
      name: 'https://compustore.co.za/',
      logo: '/client-logos/client-logo-(7).png'
    }
  ]

  return (
    <section
      id="clients"
      ref={sectionRef}
      className="bg-agency-darker py-16 overflow-hidden"
    >
      <div className="container mx-auto px-4 md:px-6 text-center">
        <h3
          ref={headingRef}
          className="text-sm uppercase tracking-wider text-agency-white-muted mb-10"
        >
          TRUSTED BY MARKET LEADERS
        </h3>

        <div
          ref={logoContainerRef}
          className="flex flex-wrap items-center justify-center gap-8 md:gap-12 lg:gap-16"
        >
          {clients.map((client, index) => (
            <div
              key={index}
              className="w-24 h-12 md:w-44 md:h-24 flex items-center justify-center grayscale hover:grayscale-0 opacity-70 hover:opacity-100 transition-all duration-300"
            >
              {/* <div className="w-full h-full bg-white/10 flex items-center justify-center rounded">
                <span className="text-white/80 text-sm">{client.name}</span>
              </div> */}
              <img src={client.logo} alt={client.name} className="w-full" />
            </div>
          ))}
        </div>
      </div>
    </section>
  )
};

export default ClientsSection;
