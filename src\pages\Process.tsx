
import React, { useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import ProcessHero from '@/components/ProcessHero';
import ProcessSteps from '@/components/ProcessSteps';
import ContactForm from '@/components/ContactForm';
import ContactCta from '@/components/ContactCta'

gsap.registerPlugin(ScrollTrigger)

const Process = () => {
  useEffect(() => {
    // Clean up on component unmount
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [])

  return (
    <div className="bg-agency-dark min-h-screen overflow-x-hidden pb-24 md:pb-0">
      <Navbar />
      <ProcessHero />
      <ProcessSteps />
      <ContactForm />
      <ContactCta />
      <Footer />
    </div>
  )
}

export default Process;
