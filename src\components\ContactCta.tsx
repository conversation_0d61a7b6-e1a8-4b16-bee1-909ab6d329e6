
import React, { useEffect, useRef } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Link } from 'react-router-dom'

gsap.registerPlugin(ScrollTrigger)

const ContactCta = () => {
  const sectionRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: sectionRef.current,
        start: 'top 80%'
      }
    })

    tl.fromTo(
      contentRef.current?.children,
      { y: 30, opacity: 0 },
      { y: 0, opacity: 1, stagger: 0.2, duration: 0.6 }
    )

    return () => {
      if (tl.scrollTrigger) {
        tl.scrollTrigger.kill()
      }
      tl.kill()
    }
  }, [])

  return (
    <section
      id="contact"
      ref={sectionRef}
      className="section-padding bg-agency-dark relative overflow-hidden"
    >
      <div className="container mx-auto">
        <div
          ref={contentRef}
          className="max-w-3xl mx-auto bg-agency-darker p-8 md:p-12 border border-white/10 rounded-lg text-center"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
            Thank you for your interest in KavaraDigital
          </h2>
          <p className="text-agency-white-muted text-lg mb-8">
            Ready to start building your next digital product? Get in touch with
            us today.
          </p>
          <Link
            to="/contact"
            className="inline-block px-8 py-3 bg-agency-green text-agency-dark font-medium rounded-md hover:bg-opacity-90 transition-all btn-glow"
          >
            Contact Us
          </Link>
        </div>
      </div>
    </section>
  )
}

export default ContactCta;
