import { useEffect } from 'react'

interface PageTitleConfig {
  title: string
  description?: string
  keywords?: string
}

// SEO-optimized page titles and meta data
export const PAGE_TITLES: Record<string, PageTitleConfig> = {
  '/': {
    title:
      'Kavara Digital - Premium Web Development & Digital Marketing Agency',
    description:
      "Transform your business with Kavara Digital's expert web development, digital marketing, and AI solutions. Get custom websites, SEO, and growth strategies that deliver results.",
    keywords:
      'web development, digital marketing, SEO, custom websites, business growth, AI solutions, Lagos Nigeria'
  },
  '/services': {
    title:
      'Our Services - Web Development, SEO & Digital Marketing | Kavara Digital',
    description:
      "Discover Kavara Digital's comprehensive services: custom web development, SEO optimization, digital marketing, e-commerce solutions, and AI integration for business growth.",
    keywords:
      'web development services, SEO services, digital marketing, e-commerce development, AI integration, business solutions'
  },
  '/pricing': {
    title:
      'Affordable Web Development & Digital Marketing Pricing | Kavara Digital',
    description:
      'Transparent pricing for premium web development and digital marketing services. Choose from our flexible packages designed to fit your budget and business goals.',
    keywords:
      'web development pricing, digital marketing costs, affordable web design, SEO pricing, business packages'
  },
  '/process': {
    title:
      'Our Development Process - How We Build Amazing Websites | Kavara Digital',
    description:
      "Learn about Kavara Digital's proven 6-step development process. From strategy to launch, discover how we create high-performing websites and digital solutions.",
    keywords:
      'web development process, design methodology, project workflow, development strategy, client collaboration'
  },
  '/work': {
    title:
      'Portfolio & Case Studies - Our Best Web Development Projects | Kavara Digital',
    description:
      "Explore Kavara Digital's portfolio of successful web development projects and case studies. See how we've helped businesses grow with custom digital solutions.",
    keywords:
      'web development portfolio, case studies, client projects, website examples, digital transformation success stories'
  },
  '/about': {
    title:
      'About Kavara Digital - Expert Web Developers & Digital Marketing Team',
    description:
      'Meet the Kavara Digital team of expert developers and digital marketers. Learn our story, mission, and commitment to delivering exceptional digital solutions.',
    keywords:
      'about kavara digital, web development team, digital marketing experts, company story, mission values'
  },
  '/contact': {
    title:
      'Contact Us Today - Get Your Free Web Development Consultation | Kavara Digital',
    description:
      'Ready to transform your business? Contact Kavara Digital today for a free consultation. Call +234 (0704) 603-8430 <NAME_EMAIL>.',
    keywords:
      'contact kavara digital, free consultation, web development quote, digital marketing consultation, Lagos Nigeria'
  },
  '/event': {
    title: 'AI & Code Workshop - Master Coding with AI Tools | Kavara Digital',
    description:
      'Join our comprehensive AI & Code Workshop on August 23rd. Learn to integrate powerful AI tools for smarter, faster development. Build real-world AI apps and master 10X developer skills.',
    keywords:
      'AI coding workshop, AI development training, coding with AI tools, AI programming course, developer skills, AI integration'
  }
}

export const usePageTitle = (pathname: string) => {
  useEffect(() => {
    const pageConfig = PAGE_TITLES[pathname] || PAGE_TITLES['/']
    
    // Set document title
    document.title = pageConfig.title
    
    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription) {
      metaDescription.setAttribute('content', pageConfig.description || '')
    } else {
      const meta = document.createElement('meta')
      meta.name = 'description'
      meta.content = pageConfig.description || ''
      document.head.appendChild(meta)
    }
    
    // Update meta keywords
    const metaKeywords = document.querySelector('meta[name="keywords"]')
    if (metaKeywords) {
      metaKeywords.setAttribute('content', pageConfig.keywords || '')
    } else {
      const meta = document.createElement('meta')
      meta.name = 'keywords'
      meta.content = pageConfig.keywords || ''
      document.head.appendChild(meta)
    }
    
    // Update Open Graph title
    const ogTitle = document.querySelector('meta[property="og:title"]')
    if (ogTitle) {
      ogTitle.setAttribute('content', pageConfig.title)
    } else {
      const meta = document.createElement('meta')
      meta.setAttribute('property', 'og:title')
      meta.content = pageConfig.title
      document.head.appendChild(meta)
    }
    
    // Update Open Graph description
    const ogDescription = document.querySelector('meta[property="og:description"]')
    if (ogDescription) {
      ogDescription.setAttribute('content', pageConfig.description || '')
    } else {
      const meta = document.createElement('meta')
      meta.setAttribute('property', 'og:description')
      meta.content = pageConfig.description || ''
      document.head.appendChild(meta)
    }
    
    // Update Twitter Card title
    const twitterTitle = document.querySelector('meta[name="twitter:title"]')
    if (twitterTitle) {
      twitterTitle.setAttribute('content', pageConfig.title)
    } else {
      const meta = document.createElement('meta')
      meta.name = 'twitter:title'
      meta.content = pageConfig.title
      document.head.appendChild(meta)
    }
    
    // Update Twitter Card description
    const twitterDescription = document.querySelector('meta[name="twitter:description"]')
    if (twitterDescription) {
      twitterDescription.setAttribute('content', pageConfig.description || '')
    } else {
      const meta = document.createElement('meta')
      meta.name = 'twitter:description'
      meta.content = pageConfig.description || ''
      document.head.appendChild(meta)
    }
    
  }, [pathname])
}

export default usePageTitle
