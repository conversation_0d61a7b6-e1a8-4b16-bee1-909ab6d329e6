
import React from 'react';
import { Mail, Phone, MapPin, Twitter, Instagram, Linkedin } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className="bg-agency-darker pt-16 pb-8 text-agency-white-muted">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-12">
          <div>
            <Link to="/" className="flex items-center mb-6">
              <img
                src="/logo-02.svg"
                alt="KavaraDigital Logo"
                className="w-50 h-16"
              />
            </Link>
            <p className="mb-6">
              We design and develop exceptional digital experiences that make an
              impact.
            </p>
            <div className="flex space-x-4">
              <a
                href="x.com/KavaraDigital"
                aria-label="Twitter"
                className="text-agency-white-muted hover:text-agency-green"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  stroke="currentColor"
                  height="24"
                  width="24"
                  viewBox="0.254 0.25 500 451.95400000000006"
                  className="w-5 h-5"
                >
                  <div
                    id="in-page-channel-node-id"
                    data-channel-name="in_page_channel_LUNtre"
                  />
                  <path
                    d="M394.033.25h76.67L303.202 191.693l197.052 260.511h-154.29L225.118 294.205 86.844 452.204H10.127l179.16-204.77L.254.25H158.46l109.234 144.417zm-26.908 406.063h42.483L135.377 43.73h-45.59z"
                    fill="#FFF"
                  />
                </svg>
              </a>
              <a
                href="https://instagram.com/kavaradigital"
                aria-label="Instagram"
                className="text-agency-white-muted hover:text-agency-green"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-5 h-5"
                >
                  <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                  <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                </svg>
              </a>
              <a
                href="https://instagram.com/kavaradigital"
                aria-label="LinkedIn"
                className="text-agency-white-muted hover:text-agency-green"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-5 h-5"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                </svg>
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-white text-lg font-bold mb-6">Useful Links</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/" className="hover:text-agency-green">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/about" className="hover:text-agency-green">
                  About
                </Link>
              </li>
              <li>
                <Link to="/services" className="hover:text-agency-green">
                  Services
                </Link>
              </li>
              <li>
                <Link to="/work" className="hover:text-agency-green">
                  Work
                </Link>
              </li>
              <li>
                <Link to="/process" className="hover:text-agency-green">
                  Process
                </Link>
              </li>
              <li>
                <Link to="/contact" className="hover:text-agency-green">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-white text-lg font-bold mb-6">What we do</h3>
            <ul className="space-y-3">
              {[
                'UI/UX Design',
                'Web Development',
                'Mobile Apps',
                'Branding',
                'AI Integration',
                'Maintenance'
              ].map((item) => (
                <li key={item}>
                  <Link to="/services" className="hover:text-agency-green">
                    {item}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-white text-lg font-bold mb-6">Contact</h3>
            <ul className="space-y-4">
              <li className="flex items-center">
                <Mail size={18} className="mr-3 text-agency-green" />
                <a
                  href="mailto:<EMAIL>"
                  className="hover:text-white"
                >
                  <EMAIL>
                </a>
              </li>
              <li className="flex items-center">
                <Phone size={18} className="mr-3 text-agency-green" />
                <a href="tel:+2347046038430" className="hover:text-white">
                  +234 (0704) 603-8430
                </a>
              </li>
              <li className="flex items-start">
                <MapPin size={18} className="mr-3 mt-1 text-agency-green" />
                <address className="not-italic">
                  No 22, Off Keji Olajide Street, 023401
                  <br />
                  Lekki Expressway, Lagos.
                </address>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-white/10 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="mb-4 md:mb-0">
            &copy; {new Date().getFullYear()} KavaraDigital. All rights
            reserved.
          </p>
          <div className="flex space-x-6">
            <a href="#" className="hover:text-white">
              Privacy Policy
            </a>
            <a href="#" className="hover:text-white">
              Terms of Service
            </a>
          </div>
        </div>
      </div>
    </footer>
  )
};

export default Footer;
