import React, { useState, useEffect } from 'react'
import { MessageCircle, X } from 'lucide-react'
import { gsap } from 'gsap'

interface WhatsAppFloatProps {
  phoneNumber?: string
  message?: string
}

const WhatsAppFloat: React.FC<WhatsAppFloatProps> = ({ 
  phoneNumber = "+2347046038430",
  message = "Hi! I'm interested in the business growth event. Can you help me secure my spot?"
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [showTooltip, setShowTooltip] = useState(false)

  useEffect(() => {
    // Show the button after 3 seconds
    const timer = setTimeout(() => {
      setIsVisible(true)
    }, 3000)

    // Show tooltip after 10 seconds if user hasn't interacted
    const tooltipTimer = setTimeout(() => {
      setShowTooltip(true)
      // Hide tooltip after 5 seconds
      setTimeout(() => setShowTooltip(false), 5000)
    }, 10000)

    return () => {
      clearTimeout(timer)
      clearTimeout(tooltipTimer)
    }
  }, [])

  useEffect(() => {
    if (isVisible) {
      gsap.fromTo('.whatsapp-float', 
        { scale: 0, rotation: 180 },
        { scale: 1, rotation: 0, duration: 0.5, ease: 'back.out(1.7)' }
      )
    }
  }, [isVisible])

  const handleWhatsAppClick = () => {
    const encodedMessage = encodeURIComponent(message)
    const whatsappUrl = `https://wa.me/${phoneNumber.replace(/[^0-9]/g, '')}?text=${encodedMessage}`
    window.open(whatsappUrl, '_blank')
    setShowTooltip(false)
  }

  if (!isVisible) return null

  return (
    <>
      {/* Tooltip */}
      {showTooltip && (
        <div className="fixed bottom-24 right-6 z-50 bg-white text-gray-800 px-4 py-2 rounded-lg shadow-lg max-w-xs animate-bounce">
          <div className="relative">
            <p className="text-sm font-medium">Need help? Chat with us!</p>
            <button 
              onClick={() => setShowTooltip(false)}
              className="absolute -top-1 -right-1 w-5 h-5 bg-gray-200 rounded-full flex items-center justify-center"
            >
              <X size={12} />
            </button>
            {/* Arrow pointing to WhatsApp button */}
            <div className="absolute -bottom-2 right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-white"></div>
          </div>
        </div>
      )}

      {/* WhatsApp Float Button */}
      <button
        onClick={handleWhatsAppClick}
        className="whatsapp-float fixed bottom-6 right-6 z-50 w-14 h-14 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group"
        aria-label="Contact us on WhatsApp"
      >
        <MessageCircle size={24} className="group-hover:scale-110 transition-transform" />
        
        {/* Pulse animation */}
        <div className="absolute inset-0 rounded-full bg-green-500 animate-ping opacity-20"></div>
      </button>
    </>
  )
}

export default WhatsAppFloat
