import React, { useEffect, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

const SALE_END_DATE = new Date('2025-07-01T23:59:59').getTime()

const packages = [
  {
    name: 'Solo Hustler Bundle',
    subtitle: 'For individuals & side hustlers starting out',
    salePrice: '₦109,250',
    oldPrice: '₦125,000',
    monthly: '₦5,500',
    renewal: '₦45,000',
    features: [
      'Home, About, Services, Contact',
      'Mobile responsive',
      'WhatsApp chat button',
      'Fast loading',
      '1 Design revision',
      'Built with WordPress',
      'Free Logo Design',
      'Branded QR Code',
      '1 Business Email Account Included'
    ],
    cta: 'Start Now',
    highlight: false
  },
  {
    name: 'SME Kickstart Deal',
    subtitle: 'Ideal for small Nigerian businesses',
    salePrice: '₦166,750',
    oldPrice: '₦195,000',
    monthly: '₦7,500',
    renewal: '₦45,000',
    features: [
      'Pages: Home, About, Services, Gallery, Contact',
      'WhatsApp & Socials',
      'Google Map, Contact Form',
      'Basic SEO, Anti-spam',
      '2 Design revisions',
      'Free Logo Design',
      '2 Social Media Flyers',
      'Free Business Card Design (2 options)',
      'Branded QR Code',
      '3 Business Email Accounts Included'
    ],
    cta: 'Build My Website',
    highlight: false
  },
  {
    name: 'Business Growth Plus',
    subtitle: 'For service professionals & growing brands',
    salePrice: '₦247,250',
    oldPrice: '₦285,000',
    monthly: '₦10,000',
    renewal: '₦105,000',
    features: [
      'Slider, Banner, Blog setup',
      'Google Analytics Integration',
      'Standard SEO',
      '3 Design revisions',
      'Mobile Optimization',
      'Free Logo Design',
      '3 Social Media Designs (IG, FB, Twitter)',
      'Business Card Design (3 styles)',
      'Branded QR Code',
      '5 Business Email Accounts Included'
    ],
    cta: 'Let’s Grow',
    highlight: true
  },
  {
    name: 'Market Seller Pro Kit',
    subtitle: 'Tailored for product sellers & small shops',
    salePrice: '₦350,750',
    oldPrice: '₦405,000',
    monthly: '₦14,500',
    renewal: '₦105,000',
    features: [
      'Custom Homepage',
      'Product, Cart, Checkout Pages',
      '25 Product Uploads',
      'Payment Gateway Integration (Paystack, Flutterwave, Stripe, PayPal)',
      'WhatsApp + Google Analytics Setup',
      'Free Logo Design',
      '4 Social Media Posters',
      'Business Card Design (3 options)',
      'Branded QR Code',
      '10 Business Email Accounts Included'
    ],
    cta: 'Launch Store',
    highlight: false
  },
  {
    name: 'AI Smart Business Hub',
    subtitle: 'For brands needing voice AI & chat automation',
    salePrice: '₦437,000',
    oldPrice: '₦495,000',
    monthly: '₦17,000',
    renewal: '₦105,000+',
    features: [
      'Custom site + AI Voice Agent',
      '24/7 Customer Support Bot',
      'Virtual Assistant Capabilities',
      'Multilingual Support',
      '4 Revisions',
      '5 Social Media Posters',
      'Business Card Design',
      'Branded QR Code',
      'Free Logo Design',
      '15 Business Email Accounts Included'
    ],
    cta: 'Get AI Setup',
    highlight: false
  },
  {
    name: 'Big Shop Nation Plan',
    subtitle: 'Built for scale: big e-commerce & stores',
    salePrice: '₦580,750',
    oldPrice: '₦667,000',
    monthly: '₦24,000',
    renewal: '₦190,000',
    features: [
      'Unlimited Products',
      'CRM & In-store Credits',
      'Advanced Google Analytics',
      'Custom Design System',
      'Google Ads Ready',
      'Payment Gateway Integration (Paystack, Flutterwave, Stripe, PayPal)',
      '5 Social Media Designs',
      'Business Card Design',
      'Free Logo (SVG, PNG, JPG)',
      'Branded QR Code',
      '25 Business Email Accounts Included'
    ],
    cta: 'Scale My Store',
    highlight: false
  },
  {
    name: 'Custom Build & Corporate Plan',
    subtitle: 'Tailored for large firms & special systems',
    salePrice: 'Contact us',
    oldPrice: '',
    monthly: 'Flexible',
    renewal: 'Flexible',
    features: [
      'Everything in Big Shop +',
      'Advanced Integrations',
      'Intranet, Client Dashboards',
      'Marketing Funnels',
      'Dedicated Team Support',
      'Flexible Email/Hosting/CRM Setup'
    ],
    cta: 'Get a Quote',
    highlight: false
  }
]

export default function PricingPage() {
  const [now, setNow] = useState(Date.now())

  useEffect(() => {
    const interval = setInterval(() => setNow(Date.now()), 1000)
    return () => clearInterval(interval)
  }, [])

  const timeLeft = SALE_END_DATE - now
  const expired = timeLeft <= 0

  const countdown = expired
    ? null
    : `${Math.floor(timeLeft / (1000 * 60 * 60 * 24))}d ${Math.floor(
        (timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      )}h ${Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60))}m`

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 md:p-8 bg-green-50">
      {packages.map((pkg, idx) => (
        <Card
          key={idx}
          className={`relative rounded-xl shadow-md border border-green-300 transition-transform transform hover:-translate-y-1 hover:shadow-xl bg-white ${
            pkg.highlight ? 'ring-2 ring-green-600' : ''
          }`}
        >
          {!expired && pkg.oldPrice && (
            <div className="absolute top-0 left-0 bg-red-600 text-white text-xs font-semibold px-3 py-1 rounded-br-xl animate-pulse shadow-lg">
              🎉 Promo Ends In: {countdown}
            </div>
          )}

          <CardContent className="p-5 flex flex-col gap-3">
            <div className="flex flex-col">
              <h2 className="text-lg font-bold text-green-900">{pkg.name}</h2>
              <p className="text-sm text-green-700">{pkg.subtitle}</p>
            </div>

            <div className="space-y-1">
              {pkg.oldPrice && !expired && (
                <p className="text-sm line-through text-red-400 font-semibold">
                  {pkg.oldPrice}
                </p>
              )}
              <p className="text-2xl font-extrabold text-green-800">
                {expired || !pkg.oldPrice ? pkg.salePrice : pkg.salePrice}
              </p>
              {pkg.renewal && (
                <p className="text-xs text-gray-500 font-medium">
                  Annual Renewal: {pkg.renewal}
                </p>
              )}
            </div>

            <ul className="text-sm text-gray-800 list-disc list-inside space-y-1">
              {pkg.features.map((feature, i) => (
                <li key={i}>{feature}</li>
              ))}
            </ul>

            {pkg.monthly && (
              <div className="mt-3 pt-3 border-t text-sm text-green-700 font-medium">
                Optional:{' '}
                <span className="font-semibold">+ {pkg.monthly}/month</span>{' '}
                maintenance & updates
              </div>
            )}

            <Button className="mt-4 bg-green-600 hover:bg-green-700 text-white w-full">
              {pkg.cta}
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
