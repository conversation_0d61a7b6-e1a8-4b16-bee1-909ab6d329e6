import React, { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import AnimatedFloatingBlocks from './AnimatedFloatingBlocks'

const PricingHero = () => {
  const titleRef = useRef<HTMLHeadingElement>(null)
  const descRef = useRef<HTMLParagraphElement>(null)

  useEffect(() => {
    const tl = gsap.timeline()

    tl.fromTo(
      titleRef.current,
      { y: 50, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: 'power3.out' }
    ).fromTo(
      descRef.current,
      { y: 30, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8, ease: 'power3.out' },
      '-=0.4'
    )

    return () => {
      tl.kill()
    }
  }, [])

  return (
    <div className="pt-24 pb-16 bg-gradient-overlay relative overflow-hidden">
      {/* Animated Floating Blocks */}
      <AnimatedFloatingBlocks />

      <div className="container mx-auto px-4 py-44 text-center relative z-10">
        <h1
          ref={titleRef}
          className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white"
        >
          Choose Your Perfect Plan
        </h1>
        <p
          ref={descRef}
          className="text-agency-white-muted max-w-3xl mx-auto text-lg"
        >
          Select from our comprehensive packages designed to meet your business
          needs. From individuals, teams, startups, to enterprise solutions, we
          have the right plan for you.
        </p>
      </div>
    </div>
  )
}

export default PricingHero
