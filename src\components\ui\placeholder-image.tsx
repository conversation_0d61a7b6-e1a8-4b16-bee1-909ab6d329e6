
import React from 'react';

interface PlaceholderImageProps {
  width?: number;
  height?: number;
  text?: string;
  className?: string;
}

const PlaceholderImage = ({
  width = 600,
  height = 400,
  text = 'KavaraDigital Project',
  className
}: PlaceholderImageProps) => {
  const placeholderUrl = `https://placehold.co/${width}x${height}/080809/39FF14?text=${encodeURIComponent(
    text
  )}`

  return (
    <img
      src={placeholderUrl}
      alt={text}
      className={className || 'w-full h-full object-cover'}
      loading="lazy"
    />
  )
}

export default PlaceholderImage;
